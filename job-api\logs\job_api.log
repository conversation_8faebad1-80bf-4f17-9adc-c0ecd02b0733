{"timestamp": "2025-07-22T01:25:45.070973", "level": "INFO", "logger": "root", "message": "Logging configuration initialized", "module": "logging_config", "function": "setup_logging", "line": 102}
{"timestamp": "2025-07-22T01:25:49.196823", "level": "WARNING", "logger": "cache", "message": "\u274c Redis connection failed: Error Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379) connecting to localhost:6379.", "module": "cache", "function": "connect", "line": 45}
{"timestamp": "2025-07-22T01:25:49.197266", "level": "INFO", "logger": "cache", "message": "Application will continue without Redis caching", "module": "cache", "function": "connect", "line": 46}
{"timestamp": "2025-07-22T01:26:06.663610", "level": "INFO", "logger": "root", "message": "Logging configuration initialized", "module": "logging_config", "function": "setup_logging", "line": 102}
{"timestamp": "2025-07-22T01:26:10.761689", "level": "WARNING", "logger": "cache", "message": "\u274c Redis connection failed: Error Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379) connecting to localhost:6379.", "module": "cache", "function": "connect", "line": 45}
{"timestamp": "2025-07-22T01:26:10.762056", "level": "INFO", "logger": "cache", "message": "Application will continue without Redis caching", "module": "cache", "function": "connect", "line": 46}
{"timestamp": "2025-07-22T01:26:19.678879", "level": "INFO", "logger": "root", "message": "Logging configuration initialized", "module": "logging_config", "function": "setup_logging", "line": 102}
{"timestamp": "2025-07-22T01:26:19.682188", "level": "INFO", "logger": "error_handlers", "message": "Error handlers configured", "module": "error_handlers", "function": "setup_error_handlers", "line": 225}
{"timestamp": "2025-07-22T01:26:23.770569", "level": "WARNING", "logger": "cache", "message": "\u274c Redis connection failed: Error Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379) connecting to localhost:6379.", "module": "cache", "function": "connect", "line": 45}
{"timestamp": "2025-07-22T01:26:23.770909", "level": "INFO", "logger": "cache", "message": "Application will continue without Redis caching", "module": "cache", "function": "connect", "line": 46}
{"timestamp": "2025-07-22T10:21:15.903446", "level": "INFO", "logger": "root", "message": "Logging configuration initialized", "module": "logging_config", "function": "setup_logging", "line": 102}
{"timestamp": "2025-07-22T10:21:15.904361", "level": "INFO", "logger": "error_handlers", "message": "Error handlers configured", "module": "error_handlers", "function": "setup_error_handlers", "line": 225}
{"timestamp": "2025-07-22T10:21:25.168250", "level": "INFO", "logger": "root", "message": "Logging configuration initialized", "module": "logging_config", "function": "setup_logging", "line": 102}
{"timestamp": "2025-07-22T10:21:25.168979", "level": "INFO", "logger": "error_handlers", "message": "Error handlers configured", "module": "error_handlers", "function": "setup_error_handlers", "line": 225}
{"timestamp": "2025-07-22T17:02:04.729081", "level": "INFO", "logger": "root", "message": "Logging configuration initialized", "module": "logging_config", "function": "setup_logging", "line": 102}
{"timestamp": "2025-07-22T17:02:04.729979", "level": "INFO", "logger": "error_handlers", "message": "Error handlers configured", "module": "error_handlers", "function": "setup_error_handlers", "line": 225}
